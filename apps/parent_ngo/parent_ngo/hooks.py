app_name = "parent_ngo"
app_title = "Parent NGO"
app_publisher = "BrainWise"
app_description = "This application, built on the Frappe Framework, is tailored specifically for Parent Non-Governmental Organizations by BrainWise to streamline and enhance their operational processes."
app_email = "<EMAIL>"
app_license = "MIT"
required_apps = ["frappe/payments", "frappe/erpnext", "BrainWise-DEV/iam", "BrainWise-DEV/license_manager_client", "BrainWise-DEV/iscore"]

# Website Context & Branding
# ----------

app_logo_url = "/assets/parent_ngo/images/branding/logo.png"

website_context = {
	"favicon": "/assets/parent_ngo/images/branding/favicon.png",
	"splash_image": "/assets/parent_ngo/images/branding/logo.png"
}

# Installation
# ------------

before_migrate = "parent_ngo.utils.install.before_migrate"
after_migrate = "parent_ngo.utils.install.after_migrate"

# Request Events
# --------------------------------

before_request = "parent_ngo.utils.app.before_request"

# API
# --------------------------------

app_apis = "parent_ngo.api.urls"
api_exceptions = "parent_ngo.exceptions"
api_responses = "parent_ngo.responses"

# Assets
# ------------------------------

app_include_js = ["workflow_pngo.bundle.js", "controls_pngo.bundle.js", "views_pngo.bundle.js", "ui_pngo.bundle.js", "reports_pngo.bundle.js", "/assets/parent_ngo/js/attachments/attach.js"]
app_include_css = ["assets/parent_ngo/css/desk.css"]

doctype_js = {
    "User": "overrides/js/user.js"
}

doctype_tree_js = {
	"Account": "overrides/js/account_tree.js"
}

# Fixtures
# ------------

fixtures = [
    "Policy",
    "Workflow",
    "Workflow State",
    "Workflow Action Master",
    {"doctype": "Role", "filters": {
        "role_name": ["in", ["All", "NGO Loan Officer", "NGO Loan Supervisor", "NGO Loan Manager", "NGO Investigator", "NGO Customer", "NGO User Creator", "NGO Accountant", "NGO E-Lending"]]
    }},
    {"doctype": "Email Template", "filters": {
        "name": ["in", ["Welcome Email", "Reset Password Email"]]
    }},
    {"doctype": "Party Type", "filters": {"name": ["in", ["Customer NGO", "Donor"]]}},
    "Role Profile",
	"Custom DocPerm",
	"Custom Role",
    "Customer NGO Attachments",
    "Loan Request Attachments",
]


# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/parent_ngo/css/parent_ngo.css"
# app_include_js = "/assets/parent_ngo/js/parent_ngo.js"

# include js, css files in header of web template
# web_include_css = "/assets/parent_ngo/css/parent_ngo.css"
# web_include_js = "/assets/parent_ngo/js/parent_ngo.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "parent_ngo/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
# doctype_js = {"doctype" : "public/js/doctype.js"}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "parent_ngo.utils.jinja_methods",
# 	"filters": "parent_ngo.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "parent_ngo.install.before_install"
# after_install = "parent_ngo.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "parent_ngo.uninstall.before_uninstall"
# after_uninstall = "parent_ngo.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "parent_ngo.utils.before_app_install"
# after_app_install = "parent_ngo.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "parent_ngo.utils.before_app_uninstall"
# after_app_uninstall = "parent_ngo.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "parent_ngo.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
# 	}
# }

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"parent_ngo.tasks.all"
# 	],
# 	"daily": [
# 		"parent_ngo.tasks.daily"
# 	],
# 	"hourly": [
# 		"parent_ngo.tasks.hourly"
# 	],
# 	"weekly": [
# 		"parent_ngo.tasks.weekly"
# 	],
# 	"monthly": [
# 		"parent_ngo.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "parent_ngo.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "parent_ngo.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "parent_ngo.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["parent_ngo.utils.before_request"]
# after_request = ["parent_ngo.utils.after_request"]

# Job Events
# ----------
# before_job = ["parent_ngo.utils.before_job"]
# after_job = ["parent_ngo.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"parent_ngo.auth.validate"
# ]
