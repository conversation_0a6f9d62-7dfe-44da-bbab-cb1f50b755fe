{"actions": [], "autoname": "naming_series:naming_series", "creation": "2024-03-13 15:40:12.217730", "default_view": "List", "doctype": "DocType", "engine": "InnoDB", "field_order": ["association_institution_data_tab", "status", "loan_request", "child_doc", "column_break_zslj", "creation_date", "investigator", "naming_series", "section_break_ptpm", "administrative_investigation_rating", "visiting_report_tab", "contact_person_details_section", "contact_full_name", "column_break_ijjc", "contact_nid", "directorate_representative_section", "directorate_full_name", "directorate_nid_front", "directorate_nid_front_preview", "column_break_d4en", "directorate_nid", "directorate_nid_back", "directorate_nid_back_preview", "section_break_pirg", "inspection_results", "column_break_wgqo", "inspection_date", "customer_ngo_address_tab", "type_of_ownership_section", "ownership_type", "association_headquarters_address", "column_break_fhkf", "ownership_date", "allocation_date", "rental_start_date", "rental_end_date", "column_break_lzla", "ownership_type_owned_rating", "ownership_type_rental_rating", "ownership_type_allocated_rating", "section_break_ycmw", "comments", "ngo_work_within_the_specified_areas_tab", "section_break_mgfs", "ngo_activities", "section_break_ppgb", "column_break_ifmi", "column_break_llbj", "beneficiaries_total", "section_break_4v56", "ngo_activities_rating", "microfinance_financing_activities_tab", "microfinance_activities", "section_break_14z0", "microfinance_activities_status", "column_break_szeo", "microfinance_activities_excellent_rating", "microfinance_activities_good_rating", "evaluating_administrative_apparatus_tab", "manager_section", "manager_full_name", "column_break_kqen", "manager_employment_date", "column_break_mvge", "manager_experience_years", "column_break_rbjw", "manager_insurance_status", "column_break_oods", "manager_rating", "accountant_section", "accountant_full_name", "column_break_beeu", "accountant_employment_date", "column_break_ghoo", "accountant_experience_years", "column_break_zgyn", "accountant_insurance_status", "column_break_xdiw", "accountant_rating", "administrative_officer_section", "officer_full_name", "column_break_epkj", "officer_employment_date", "column_break_gazy", "officer_experience_years", "column_break_sjyf", "officer_insurance_status", "column_break_gwpc", "officer_rating", "cashier_section", "cashier_full_name", "column_break_mbav", "cashier_employment_date", "column_break_weod", "cashier_experience_years", "column_break_gpur", "cashier_insurance_status", "column_break_jffj", "cashier_rating", "social_investigator_section", "investigator_full_name", "column_break_xqkl", "investigator_employment_date", "column_break_tjtp", "investigator_experience_years", "column_break_weuo", "investigator_insurance_status", "column_break_elan", "investigator_rating", "section_break_bwmn", "administrative_apparatus_rating", "organizational_chart_tab", "general_assembly_section", "ga_males", "ga_females", "general_assembly_memebers_rating", "general_assembly_meetings_rating", "column_break_ugub", "ga_meetings", "ga_last_meeting_date", "general_assembly_females_members_rating", "board_of_directors_section", "bod_males", "bod_females", "bod_committees", "directors_board_meetings_rating", "directors_board_females_members_rating", "column_break_dsmt", "bod_meetings", "bod_last_meeting_date", "directors_board_members_rating_5_9", "directors_board_members_rating_11_15", "section_break_ffkx", "organizational_chart_rating", "general_comment_tab", "committee_decision", "comment"], "fields": [{"fieldname": "visiting_report_tab", "fieldtype": "Tab Break", "label": "Visiting Report"}, {"fieldname": "contact_person_details_section", "fieldtype": "Section Break", "label": "Contact Person Details"}, {"fieldname": "column_break_ijjc", "fieldtype": "Column Break"}, {"fieldname": "directorate_representative_section", "fieldtype": "Section Break", "label": "Directorate Representative"}, {"fieldname": "column_break_d4en", "fieldtype": "Column Break"}, {"fieldname": "section_break_pirg", "fieldtype": "Section Break", "label": "Latest Financial & Administrative Inspection"}, {"fieldname": "column_break_wgqo", "fieldtype": "Column Break"}, {"fieldname": "customer_ngo_address_tab", "fieldtype": "Tab Break", "label": "NGO Address"}, {"fieldname": "type_of_ownership_section", "fieldtype": "Section Break", "label": "Ownership"}, {"depends_on": "eval: doc.ownership_type == \"Owned\"", "fieldname": "ownership_date", "fieldtype": "Date", "label": "Ownership Date"}, {"depends_on": "eval: doc.ownership_type == \"Rental\"", "fieldname": "rental_start_date", "fieldtype": "Date", "label": "Rental Start Date"}, {"depends_on": "eval: doc.ownership_type == \"Rental\"", "fieldname": "rental_end_date", "fieldtype": "Date", "label": "Rental End Date"}, {"fieldname": "section_break_ycmw", "fieldtype": "Section Break"}, {"fieldname": "comments", "fieldtype": "Text", "label": "Comments"}, {"fieldname": "ngo_work_within_the_specified_areas_tab", "fieldtype": "Tab Break", "label": "NGO Activities"}, {"fieldname": "section_break_4v56", "fieldtype": "Section Break"}, {"fieldname": "microfinance_financing_activities_tab", "fieldtype": "Tab Break", "label": "Microfinance Activities"}, {"fieldname": "section_break_14z0", "fieldtype": "Section Break"}, {"fieldname": "evaluating_administrative_apparatus_tab", "fieldtype": "Tab Break", "label": "Administrative Apparatus"}, {"fieldname": "organizational_chart_tab", "fieldtype": "Tab Break", "label": "Organizational Chart"}, {"fieldname": "general_assembly_section", "fieldtype": "Section Break", "label": "General Assembly"}, {"fieldname": "column_break_ugub", "fieldtype": "Column Break"}, {"fieldname": "board_of_directors_section", "fieldtype": "Section Break", "label": "Board of Directors"}, {"fieldname": "column_break_dsmt", "fieldtype": "Column Break"}, {"fieldname": "general_comment_tab", "fieldtype": "Tab Break", "label": "Committee Decision"}, {"fieldname": "comment", "fieldtype": "Text", "label": "Comment"}, {"default": "Opened", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Opened\nIn Progress\nCompleted\nCancelled", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_zslj", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "creation_date", "fieldtype": "Date", "label": "Creation Date", "read_only": 1, "reqd": 1}, {"fieldname": "investigator", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Investigator", "options": "User", "reqd": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "hidden": 1, "label": "Naming Series", "no_copy": 1, "options": "AI-.#", "read_only": 1, "set_only_once": 1}, {"fieldname": "contact_full_name", "fieldtype": "Data", "label": "Full Name"}, {"fieldname": "contact_nid", "fieldtype": "Data", "label": "National ID", "length": 14}, {"fieldname": "directorate_full_name", "fieldtype": "Data", "label": "Full Name"}, {"fieldname": "directorate_nid", "fieldtype": "Data", "label": "National ID", "length": 14}, {"depends_on": "eval: !doc.__islocal", "fieldname": "directorate_nid_front", "fieldtype": "Attach Image", "label": "National ID Front"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "directorate_nid_back", "fieldtype": "Attach Image", "label": "National ID Back"}, {"depends_on": "directorate_nid_front", "fieldname": "directorate_nid_front_preview", "fieldtype": "Image", "options": "directorate_nid_front"}, {"depends_on": "directorate_nid_back", "fieldname": "directorate_nid_back_preview", "fieldtype": "Image", "options": "directorate_nid_back"}, {"fieldname": "inspection_results", "fieldtype": "Text", "label": "Inspection Results"}, {"fieldname": "inspection_date", "fieldtype": "Date", "label": "Inspection Date"}, {"fieldname": "ownership_type", "fieldtype": "Select", "label": "Type of Ownership", "options": "\nOwned\nAllocated\nRental"}, {"fieldname": "column_break_fhkf", "fieldtype": "Column Break"}, {"fieldname": "section_break_mgfs", "fieldtype": "Section Break"}, {"fieldname": "ngo_activities", "fieldtype": "Table", "label": "Activities", "options": "NGO Activities"}, {"fieldname": "section_break_ppgb", "fieldtype": "Section Break"}, {"fieldname": "column_break_ifmi", "fieldtype": "Column Break"}, {"fieldname": "column_break_llbj", "fieldtype": "Column Break"}, {"fieldname": "beneficiaries_total", "fieldtype": "Int", "label": "Total Number of Beneficiaries", "non_negative": 1, "read_only": 1}, {"fieldname": "microfinance_activities", "fieldtype": "Table", "label": "Activities", "options": "Microfinance Activities"}, {"fieldname": "ga_males", "fieldtype": "Int", "label": "Number of Males", "non_negative": 1}, {"fieldname": "ga_females", "fieldtype": "Int", "label": "Number of Females", "non_negative": 1}, {"fieldname": "ga_meetings", "fieldtype": "Int", "label": "Number of Meetings", "non_negative": 1}, {"fieldname": "ga_last_meeting_date", "fieldtype": "Date", "label": "Last Meeting Date"}, {"fieldname": "bod_males", "fieldtype": "Int", "label": "Number of Males", "non_negative": 1}, {"fieldname": "bod_females", "fieldtype": "Int", "label": "Number of Females", "non_negative": 1}, {"fieldname": "bod_meetings", "fieldtype": "Int", "label": "Number of Meetings", "non_negative": 1}, {"fieldname": "bod_last_meeting_date", "fieldtype": "Date", "label": "Last Meeting Date"}, {"fieldname": "bod_committees", "fieldtype": "Int", "label": "Number of Formed Committees", "non_negative": 1}, {"fieldname": "loan_request", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Loan Request", "options": "Loan Request", "reqd": 1}, {"fieldname": "child_doc", "fieldtype": "Data", "hidden": 1, "label": "Child Document", "no_copy": 1, "read_only": 1}, {"depends_on": "eval: doc.ownership_type == \"Allocated\"", "fieldname": "allocation_date", "fieldtype": "Date", "label": "Allocation Date"}, {"fieldname": "association_institution_data_tab", "fieldtype": "Tab Break", "label": "Association (Institution) Data"}, {"depends_on": "eval: doc.ownership_type", "fieldname": "association_headquarters_address", "fieldtype": "Text", "label": "Association Headquarters Address"}, {"fieldname": "committee_decision", "fieldtype": "Select", "label": "Committee Decision", "options": "\nApproval of financing the association\nRefusal of financing the association\nPostponing the decision until the next field visit"}, {"fieldname": "column_break_lzla", "fieldtype": "Column Break"}, {"depends_on": "eval: doc.ownership_type == \"Owned\"", "fieldname": "ownership_type_owned_rating", "fieldtype": "Select", "label": "Type of Ownership Rating", "mandatory_depends_on": "eval: doc.ownership_type == \"Owned\"", "options": "\n8\n7\n6\n5\n4"}, {"depends_on": "eval: doc.ownership_type == \"Rental\"", "fieldname": "ownership_type_rental_rating", "fieldtype": "Select", "label": "Type of Ownership Rating", "mandatory_depends_on": "eval: doc.ownership_type == \"Rental\"", "options": "\n3\n2\n1"}, {"depends_on": "eval: doc.ownership_type == \"Allocated\"", "fieldname": "ownership_type_allocated_rating", "fieldtype": "Select", "label": "Type of Ownership Rating", "mandatory_depends_on": "eval: doc.ownership_type == \"Allocated\"", "options": "\n10\n9"}, {"fieldname": "microfinance_activities_status", "fieldtype": "Select", "label": "Microfinance Activities Status", "options": "\nExcellent\nGood"}, {"fieldname": "column_break_szeo", "fieldtype": "Column Break"}, {"depends_on": "eval: doc.microfinance_activities_status == \"Excellent\"", "fieldname": "microfinance_activities_excellent_rating", "fieldtype": "Select", "label": "Microfinance Activities Rating", "mandatory_depends_on": "eval: doc.microfinance_activities_status == \"Excellent\"", "options": "5\n4\n3"}, {"depends_on": "eval: doc.microfinance_activities_status == \"Good\"", "fieldname": "microfinance_activities_good_rating", "fieldtype": "Select", "label": "Microfinance Activities Rating", "mandatory_depends_on": "eval: doc.microfinance_activities_status == \"Good\"", "options": "2\n1\n0"}, {"fieldname": "general_assembly_memebers_rating", "fieldtype": "Select", "label": "General Assembly Members Rating", "options": "\n1\n0"}, {"fieldname": "general_assembly_females_members_rating", "fieldtype": "Select", "label": "General Assembly Females Members Rating", "options": "\n2\n1\n0"}, {"fieldname": "general_assembly_meetings_rating", "fieldtype": "Select", "label": "General Assembly Meetings Rating", "options": "\n2\n1\n0"}, {"depends_on": "eval: doc.bod_males + doc.bod_females >= 5 && doc.bod_males + doc.bod_females <= 9", "fieldname": "directors_board_members_rating_5_9", "fieldtype": "Select", "label": "Directors Board Members Rating", "mandatory_depends_on": "eval: doc.bod_males + doc.bod_females >= 5 && doc.bod_males + doc.bod_females <= 9", "options": "\n1\n0"}, {"depends_on": "eval: doc.bod_males + doc.bod_females >= 10", "fieldname": "directors_board_members_rating_11_15", "fieldtype": "Select", "label": "Directors Board Members Rating", "mandatory_depends_on": "eval: doc.bod_males + doc.bod_females >= 10", "options": "\n2\n1\n0"}, {"fieldname": "directors_board_meetings_rating", "fieldtype": "Select", "label": "Directors Board Meetings Rating", "options": "\n1\n0"}, {"fieldname": "directors_board_females_members_rating", "fieldtype": "Select", "label": "Directors Board Females Members Rating", "options": "\n2\n1\n0"}, {"fieldname": "section_break_ffkx", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "organizational_chart_rating", "fieldtype": "Int", "label": "Organizational Chart Rating", "non_negative": 1, "read_only": 1}, {"fieldname": "manager_section", "fieldtype": "Section Break", "label": "Manager"}, {"fieldname": "manager_full_name", "fieldtype": "Data", "label": "Full Name"}, {"fieldname": "column_break_kqen", "fieldtype": "Column Break"}, {"fieldname": "column_break_mvge", "fieldtype": "Column Break"}, {"fieldname": "column_break_rbjw", "fieldtype": "Column Break"}, {"fieldname": "column_break_oods", "fieldtype": "Column Break"}, {"fieldname": "manager_employment_date", "fieldtype": "Date", "label": "Employment Date"}, {"default": "0", "fieldname": "manager_experience_years", "fieldtype": "Int", "label": "Experience Years", "non_negative": 1}, {"fieldname": "manager_insurance_status", "fieldtype": "Select", "label": "Insurance Status", "options": "\nInsured\nNot Insured"}, {"fieldname": "accountant_section", "fieldtype": "Section Break", "label": "Accountant"}, {"fieldname": "column_break_beeu", "fieldtype": "Column Break"}, {"fieldname": "column_break_ghoo", "fieldtype": "Column Break"}, {"fieldname": "column_break_zgyn", "fieldtype": "Column Break"}, {"fieldname": "column_break_xdiw", "fieldtype": "Column Break"}, {"fieldname": "accountant_full_name", "fieldtype": "Data", "label": "Full Name"}, {"fieldname": "accountant_employment_date", "fieldtype": "Date", "label": "Employment Date"}, {"default": "0", "fieldname": "accountant_experience_years", "fieldtype": "Int", "label": "Experience Years", "non_negative": 1}, {"fieldname": "accountant_insurance_status", "fieldtype": "Select", "label": "Insurance Status", "options": "\nInsured\nNot Insured"}, {"fieldname": "administrative_officer_section", "fieldtype": "Section Break", "label": "Administrative Officer"}, {"fieldname": "column_break_epkj", "fieldtype": "Column Break"}, {"fieldname": "column_break_gazy", "fieldtype": "Column Break"}, {"fieldname": "column_break_sjyf", "fieldtype": "Column Break"}, {"fieldname": "column_break_gwpc", "fieldtype": "Column Break"}, {"fieldname": "officer_full_name", "fieldtype": "Data", "label": "Full Name"}, {"fieldname": "officer_employment_date", "fieldtype": "Date", "label": "Employment Date"}, {"default": "0", "fieldname": "officer_experience_years", "fieldtype": "Int", "label": "Experience Years", "non_negative": 1}, {"fieldname": "officer_insurance_status", "fieldtype": "Select", "label": "Insurance Status", "options": "\nInsured\nNot Insured"}, {"fieldname": "cashier_section", "fieldtype": "Section Break", "label": "Cashier"}, {"fieldname": "column_break_mbav", "fieldtype": "Column Break"}, {"fieldname": "column_break_weod", "fieldtype": "Column Break"}, {"fieldname": "column_break_gpur", "fieldtype": "Column Break"}, {"fieldname": "column_break_jffj", "fieldtype": "Column Break"}, {"fieldname": "cashier_full_name", "fieldtype": "Data", "label": "Full Name"}, {"fieldname": "cashier_employment_date", "fieldtype": "Date", "label": "Employment Date"}, {"default": "0", "fieldname": "cashier_experience_years", "fieldtype": "Int", "label": "Experience Years", "non_negative": 1}, {"fieldname": "cashier_insurance_status", "fieldtype": "Select", "label": "Insurance Status", "options": "\nInsured\nNot Insured"}, {"fieldname": "social_investigator_section", "fieldtype": "Section Break", "label": "Social Investigator"}, {"fieldname": "column_break_xqkl", "fieldtype": "Column Break"}, {"fieldname": "column_break_tjtp", "fieldtype": "Column Break"}, {"fieldname": "column_break_weuo", "fieldtype": "Column Break"}, {"fieldname": "column_break_elan", "fieldtype": "Column Break"}, {"fieldname": "investigator_full_name", "fieldtype": "Data", "label": "Full Name"}, {"fieldname": "investigator_employment_date", "fieldtype": "Date", "label": "Employment Date"}, {"default": "0", "fieldname": "investigator_experience_years", "fieldtype": "Int", "label": "Experience Years", "non_negative": 1}, {"fieldname": "investigator_insurance_status", "fieldtype": "Select", "label": "Insurance Status", "options": "\nInsured\nNot Insured"}, {"default": "0", "fieldname": "accountant_rating", "fieldtype": "Select", "label": "Rating", "options": "0\n1\n2\n3"}, {"default": "0", "fieldname": "officer_rating", "fieldtype": "Select", "label": "Rating", "options": "0\n1\n2\n3"}, {"default": "0", "fieldname": "cashier_rating", "fieldtype": "Select", "label": "Rating", "options": "0\n1\n2"}, {"default": "0", "fieldname": "investigator_rating", "fieldtype": "Select", "label": "Rating", "options": "0\n1\n2"}, {"default": "0", "fieldname": "manager_rating", "fieldtype": "Select", "label": "Rating", "options": "0\n1\n2\n3\n4\n5"}, {"fieldname": "section_break_bwmn", "fieldtype": "Section Break"}, {"fieldname": "administrative_apparatus_rating", "fieldtype": "Int", "label": "Administrative Apparatus Rating", "non_negative": 1, "read_only": 1, "reqd": 1}, {"depends_on": "eval: doc.ngo_activities && doc.ngo_activities.length > 0", "description": "Rating depends on the Lending Status of NGO Activities", "fieldname": "ngo_activities_rating", "fieldtype": "Select", "label": "NGO Activities Rating", "mandatory_depends_on": "eval: doc.ngo_activities && doc.ngo_activities.length > 0"}, {"fieldname": "section_break_ptpm", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "administrative_investigation_rating", "fieldtype": "Int", "label": "Administrative Investigation Rating", "non_negative": 1, "read_only": 1, "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-16 15:53:09.469358", "modified_by": "Administrator", "module": "Parent NGO", "name": "Administrative Investigation", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Supervisor", "share": 1}, {"delete": 1, "if_owner": 1, "role": "NGO Loan Supervisor", "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Officer", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Manager", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}