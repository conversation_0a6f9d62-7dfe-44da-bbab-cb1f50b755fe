2025-06-16 15:07:27,482 WARNING database DDL Query made to DB:
create table `tabMicrofinance Activities` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`project_name` varchar(140),
`project_start_date` date,
`project_financing_amount` decimal(21,9) not null default 0,
`beneficiaries_total` int(11) not null default 0,
`donor` varchar(140),
`total_financing_amount` decimal(21,9) not null default 0,
`lending_type` varchar(140),
`repayment_status` varchar(140),
`lending_status` varchar(140),
`end_date` date,
`comment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:28,061 WARNING database DDL Query made to DB:
create table `tabLoan Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`program_name` varchar(140) unique,
`interest_rate` decimal(21,9) not null default 0,
`installment_period` int(11) not null default 0,
`donor` varchar(140),
`interest_receivable_account` varchar(140),
`loan_receivable_account` varchar(140),
`interest_income_account` varchar(140),
`min_amount` decimal(21,9) not null default 0,
`max_amount` decimal(21,9) not null default 0,
`fund_pool` decimal(21,9) not null default 0,
`sanctioned_funds` decimal(21,9) not null default 0,
`remaining_funds` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:28,270 WARNING database DDL Query made to DB:
create table `tabLoan Program Project Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`project_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:28,548 WARNING database DDL Query made to DB:
create table `tabLoan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Sanctioned',
`loan_request` varchar(140),
`ngo` varchar(140),
`ngo_name` varchar(140),
`posting_date` date,
`repayment_start_date` date,
`disbursement_date` date,
`closure_date` date,
`loan_type` varchar(140),
`loan_program` varchar(140),
`loan_amount` decimal(21,9) not null default 0,
`undisbursed_amount` decimal(21,9) not null default 0,
`disbursed_amount` decimal(21,9) not null default 0,
`interest_rate` decimal(21,9) not null default 0,
`repayment_period` int(11) not null default 0,
`remaining_payments` int(11) not null default 0,
`naming_series` varchar(140),
`amended_from` varchar(140),
`interest_receivable_account` varchar(140),
`loan_receivable_account` varchar(140),
`interest_income_account` varchar(140),
`total_amount` decimal(21,9) not null default 0,
`amount_paid` decimal(21,9) not null default 0,
`remaining_amount` decimal(21,9) not null default 0,
`total_principal` decimal(21,9) not null default 0,
`principal_paid` decimal(21,9) not null default 0,
`remaining_principal_amount` decimal(21,9) not null default 0,
`total_interest` decimal(21,9) not null default 0,
`interest_paid` decimal(21,9) not null default 0,
`remaining_interest_amount` decimal(21,9) not null default 0,
`monthly_repayment_amount` decimal(21,9) not null default 0,
`monthly_principal_amount` decimal(21,9) not null default 0,
`monthly_interest_amount` decimal(21,9) not null default 0,
`last_monthly_repayment_amount` decimal(21,9) not null default 0,
`last_monthly_principal_amount` decimal(21,9) not null default 0,
`last_monthly_interest_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:28,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` ADD COLUMN `bm_iscore_status` varchar(140), ADD COLUMN `bm_iscore_result` int(11) not null default 0, ADD COLUMN `bm_iscore_loans_number` int(11) not null default 0, ADD COLUMN `bm_iscore_total_balance` decimal(21,9) not null default 0, ADD COLUMN `bm_iscore_report` text, ADD COLUMN `bm_iscore_doc` varchar(140), ADD COLUMN `bm_iscore_report_id` varchar(140)
2025-06-16 15:07:28,936 WARNING database DDL Query made to DB:
create table `tabFinancial Follow Up` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Opened',
`loan` varchar(140),
`creation_date` date,
`investigator` varchar(140),
`naming_series` varchar(140),
`loan_cycle` varchar(140),
`bank_book_statement` varchar(140),
`loans_customers_record` varchar(140),
`daily_american_journals` varchar(140),
`comment` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:29,089 WARNING database DDL Query made to DB:
create table `tabLoan Program Governorate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`governorate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:29,222 WARNING database DDL Query made to DB:
create table `tabRepayment Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_date` date,
`paid_on` date,
`principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`total_paid` decimal(21,9) not null default 0,
`is_paid` int(1) not null default 0,
`loan_repayment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:29,354 WARNING database DDL Query made to DB:
create table `tabLoan Disbursement Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Draft',
`request_date` date,
`loan` varchar(140),
`ngo` varchar(140),
`disbursement_amount` decimal(21,9) not null default 0,
`naming_series` varchar(140),
`registry` text,
`bank_statement` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:29,571 WARNING database DDL Query made to DB:
create table `tabLoan Repayment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`ngo` varchar(140),
`due_date` date,
`repayment_date` date,
`principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`total_paid` decimal(21,9) not null default 0,
`repayment_account` varchar(140),
`loan_receivable_account` varchar(140),
`interest_receivable_account` varchar(140),
`reference_number` varchar(140),
`payment_term_id` varchar(140),
`journal_entry` varchar(140),
`reference_date` date,
`payment_term_idx` int(11) not null default 0,
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `loan`(`loan`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:29,736 WARNING database DDL Query made to DB:
create table `tabLR Beneficiary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`nid` varchar(14),
`full_name` varchar(140),
`loan_amount` decimal(21,9) not null default 0,
`project_description` text,
`governorate` varchar(140),
`beneficiary_doc` varchar(140),
`child_doc` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:29,869 WARNING database DDL Query made to DB:
create table `tabAdministrative Follow Up` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Opened',
`loan` varchar(140),
`creation_date` date,
`investigator` varchar(140),
`naming_series` varchar(140),
`disbursement_aspects` varchar(140),
`customers_book` varchar(140),
`projects_sample_fu` varchar(140),
`projects_classification` varchar(140),
`comment` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `loan`(`loan`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:30,028 WARNING database DDL Query made to DB:
create table `tabExtra Documents` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:30,145 WARNING database DDL Query made to DB:
create table `tabLDR Beneficiary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`nid` varchar(14),
`full_name` varchar(140),
`loan_amount` decimal(21,9) not null default 0,
`received_loan` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:30,385 WARNING database DDL Query made to DB:
create table `tabLoan Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Draft',
`loan_status` varchar(140),
`loan` varchar(140),
`creation_date` date,
`naming_series` varchar(140),
`ngo` varchar(140),
`ngo_name` varchar(140),
`publication_number` varchar(140),
`license_number` varchar(140),
`address` varchar(140),
`email` varchar(140),
`publication_date` date,
`license_date` date,
`contact_full_name` varchar(140),
`contact_email` varchar(140),
`contact_nid` varchar(140),
`contact_mobile_no` varchar(140),
`contact_nid_front` text,
`contact_nid_back` text,
`treasurer_full_name` varchar(140),
`treasurer_nid` varchar(140),
`treasurer_nid_front` text,
`treasurer_nid_back` text,
`treasurer_iscore_status` varchar(140),
`treasurer_iscore_result` int(11) not null default 0,
`treasurer_iscore_loans_number` int(11) not null default 0,
`treasurer_iscore_total_balance` decimal(21,9) not null default 0,
`treasurer_iscore_doc` varchar(140),
`treasurer_iscore_report` text,
`treasurer_iscore_report_id` varchar(140),
`ceo_full_name` varchar(140),
`ceo_nid` varchar(140),
`ceo_nid_front` text,
`ceo_nid_back` text,
`ceo_iscore_status` varchar(140),
`ceo_iscore_result` int(11) not null default 0,
`ceo_iscore_loans_number` int(11) not null default 0,
`ceo_iscore_total_balance` decimal(21,9) not null default 0,
`ceo_iscore_report` text,
`ceo_iscore_doc` varchar(140),
`ceo_iscore_report_id` varchar(140),
`total_loan_amount` decimal(21,9) not null default 0,
`loan_type` varchar(140),
`requested_loan_amount` decimal(21,9) not null default 0,
`loan_program` varchar(140),
`loan_program_name` varchar(140),
`interest_rate` decimal(21,9) not null default 0,
`installment_period` int(11) not null default 0,
`terms` longtext,
`accept_terms` int(1) not null default 0,
`hc_report` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:30,601 WARNING database DDL Query made to DB:
create table `tabLoan Disbursement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`ngo` varchar(140),
`disbursement_date` date,
`disbursed_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`disbursement_account` varchar(140),
`loan_receivable_account` varchar(140),
`interest_receivable_account` varchar(140),
`interest_income_account` varchar(140),
`front_nid` text,
`ngo_authorization` text,
`bank_approval` text,
`disbursement_permission` text,
`received_cheque_doc` text,
`installment_cheques` text,
`back_nid` text,
`signed_stamped_repayment_schedule` text,
`reference_date` date,
`journal_entry` varchar(140),
`reference_number` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:30,774 WARNING database DDL Query made to DB:
create table `tabAttachments Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:07:31,108 WARNING database DDL Query made to DB:
create table `tabAttachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140),
`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-16 15:53:10,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` MODIFY `general_assembly_meetings_rating` varchar(140)
