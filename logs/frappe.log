2025-06-10 14:44:22,285 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-10 14:50:45,230 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-10 14:50:47,059 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-10 15:15:44,248 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-10 23:16:50,462 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doc': '{"name":"LR-57","owner":"Administrator","creation":"2025-06-10 22:30:23.803031","modified":"2025-06-10 22:51:01.983789","modified_by":"Administrator","docstatus":0,"idx":24,"status":"Pending i-Score","creation_date":"2025-06-10","naming_series":"LR-.#.","ngo":"taj","ngo_name":"taj","publication_number":"9088","license_number":"2526","address":"ffdedwf","email":"<EMAIL>","publication_date":"2000-11-25","license_date":"2000-12-28","contact_full_name":"taj elden123","contact_email":"<EMAIL>","contact_nid":"29907151201775","contact_mobile_no":"01221933915","contact_nid_front":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","contact_nid_back":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","treasurer_full_name":"mero","treasurer_nid":"29909181601442","treasurer_nid_front":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","treasurer_nid_back":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","treasurer_iscore_status":"Failed","treasurer_iscore_result":12,"treasurer_iscore_loans_number":0,"treasurer_iscore_total_balance":0,"treasurer_iscore_doc":"ISC-2025-06-10-23","ceo_full_name":"bljsj","ceo_nid":"28808141501326","ceo_nid_front":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","ceo_nid_back":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","ceo_iscore_status":"Failed","ceo_iscore_result":2222,"ceo_iscore_loans_number":0,"ceo_iscore_total_balance":0,"ceo_iscore_doc":"ISC-2025-06-10-24","total_loan_amount":12000,"loan_type":"Revolving Loan","requested_loan_amount":12000,"loan_program":"برنامج الحد من الفقر في الجيزه","loan_program_name":"برنامج الحد من الفقر في الجيزه","interest_rate":2,"installment_period":2,"lr_document":"/private/files/loan-request-lr-1-تكافل-المصريين-953fc.png","fra_permit":"/private/files/loan-request-lr-1-تكافل-المصريين-953fc.png","bod_statement":"/private/files/loan-request-lr-1-تكافل-المصريين-953fc.png","oc_statement":"/private/files/loan-request-lr-1-تكافل-المصريين-953fc.png","terms":"<div class=\\"ql-editor read-mode\\"><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl ql-indent-8\\"><strong style=\\"color: var(--heading-color); font-size: 32px;\\">إقرار وتعهد بسداد الاقساط</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl ql-indent-8\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بأن جميع المستندات المقدمة مني الي المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 بغرض</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;الحصول على مبلغ من المال سليمة وصحيحة ومطابقة للأصل وعلى مسئوليتي الخاصة</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;وأكون متحملا المسؤولية الجنائية&nbsp;في حالة ثبوت عدم صحتها ودون أدنى مسئولية عليهم.</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بأن العنوان الموضح لدى المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 والخاص بمزاولة النشاط</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;والخاص بإقامتي هو العنوان المختار لي ويحق لهم ارسال الإنذارات والمكاتبات والمراسلات عليه..</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;وفي حالة تغيير محل الإقامة او مزاولة النشاط التزم بإخطارهم خلال أسبوع من تاريخ التغيير</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;بخطاب مسجل بعلم الوصول أو إنذار وفي حالة عدم إخطارهم بتغيير محل الإقامة او مزاولة</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;النشاط يحق لهم إعلاني على العنوان الموضح (السابق).</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بعدم تسليم أي مبالغ نقدية الا لأمين خزينة المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 والحصول</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;على إيصال الاستلام من الخزينة وفي حالة تسليم أي مبالغ نقدية لأي موظف تكون المؤسسة القومية</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;لتنمية الأسرة والمجتمع \u2069 غير مسئولة عنها ويحق لهم الرجوع على بالسداد كما اقر بموافقتي على</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;غرامة التأخير المقررة بمعرفة المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 وليس لدي أي اعتراض</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;عليها ويحق لهم تحصيلها بالكامل ولا يجوز لي الطعن عليها.</strong></h3><p><br></p></div>","accept_terms":1,"doctype":"Loan Request","extra_documents":[],"financial_investigations":[],"board_members":[{"name":"iiilgp246v","owner":"Administrator","creation":"2025-06-10 22:30:23.803031","modified":"2025-06-10 22:51:02.312376","modified_by":"Administrator","docstatus":0,"idx":1,"full_name":"momomo","nid":"28711140122551","mobile_no":"***********","ngo_occupation":"vzcxzc","nid_front":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","nid_back":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","bm_iscore_status":"Failed","bm_iscore_result":0,"bm_iscore_loans_number":0,"bm_iscore_total_balance":0,"bm_iscore_doc":"ISC-2025-06-10-25","parent":"LR-57","parentfield":"board_members","parenttype":"Loan Request","doctype":"LR Board Member Details","bm_iscore_report":"/private/files/email-account-new-email-account-dolhibpgmq-30430.png","bm_iscore_report_id":"bd14287154"},{"name":"fptcq1a910","owner":"Administrator","creation":"2025-06-10 22:30:23.803031","modified":"2025-06-10 22:51:02.623574","modified_by":"Administrator","docstatus":0,"idx":2,"full_name":" regsvrd","nid":"**************","mobile_no":"***********","ngo_occupation":"sdgf","nid_front":"/private/files/loan-request-lr-1-تكافل-المصريين-953fc.png","nid_back":"/private/files/loan-request-lr-1-تكافل-المصريين-953fc.png","bm_iscore_status":"Failed","bm_iscore_result":0,"bm_iscore_loans_number":0,"bm_iscore_total_balance":0,"bm_iscore_doc":"ISC-2025-06-10-26","parent":"LR-57","parentfield":"board_members","parenttype":"Loan Request","doctype":"LR Board Member Details","bm_iscore_report":"/private/files/email-account-new-email-account-dolhibpgmq-30430.png","bm_iscore_report_id":"c0facc8282"}],"administrative_investigations":[],"beneficiaries":[{"name":"mg4fdpf8bo","owner":"Administrator","creation":"2025-06-10 22:30:23.803031","modified":"2025-06-10 22:51:01.983789","modified_by":"Administrator","docstatus":0,"idx":1,"nid":"**************","full_name":"vsdsd","loan_amount":12000,"project_description":"sdfdd","governorate":"Bani Suef","beneficiary_doc":"**************","child_doc":"q26icrmeri","parent":"LR-57","parentfield":"beneficiaries","parenttype":"Loan Request","doctype":"LR Beneficiary"}],"__last_sync_on":"2025-06-10T20:15:47.595Z","treasurer_iscore_report":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","__unsaved":1,"treasurer_iscore_report_id":"51cc8dece8","ceo_iscore_report":"/files/administrative-investigation-ai-28-6c404.jpg","ceo_iscore_report_id":"67dd8b490d"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-12 15:05:20,367 ERROR frappe Could not take error snapshot: No module named 'myhr'
Site: customhr
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 99, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 172, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1650, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1498, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1463, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1493, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'
2025-06-12 15:06:15,227 ERROR frappe Could not take error snapshot: No module named 'myhr'
Site: customhr
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 99, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 172, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1650, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1498, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1463, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1493, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'
2025-06-12 15:06:16,422 ERROR frappe Could not take error snapshot: No module named 'myhr'
Site: customhr
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 99, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 172, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1650, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1498, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1463, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1493, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'
2025-06-12 15:06:17,394 ERROR frappe Could not take error snapshot: No module named 'myhr'
Site: customhr
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 99, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 172, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1650, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1498, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1463, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1493, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'myhr'
2025-06-12 15:42:27,385 ERROR frappe New Exception collected in error log
Site: customhr
Form Dict: {'report_name': 'Drivers and Authorized Vehicles', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-06-12 15:43:21,053 ERROR frappe New Exception collected in error log
Site: customhr
Form Dict: {'report_name': 'Drivers and Authorized Vehicles', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-06-12 15:47:02,003 ERROR frappe New Exception collected in error log
Site: customhr
Form Dict: {'report_name': 'Drivers and Authorized Vehicles', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-06-12 16:03:24,334 ERROR frappe New Exception collected in error log
Site: customhr
Form Dict: {'report_name': 'Drivers and Authorized Vehicles', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-06-12 16:03:55,111 ERROR frappe New Exception collected in error log
Site: customhr
Form Dict: {'report_name': 'Drivers and Authorized Vehicles', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-06-15 11:23:45,366 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:47,265 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:48,206 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:49,233 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:49,883 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:50,808 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:52,214 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:53,266 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:55,088 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:55,743 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:04,082 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:06,558 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:09,272 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:10,843 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:10,885 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:14,389 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:18,450 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:20,356 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:22,779 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:23,820 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:25,313 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:44,264 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:25:01,131 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:25:02,306 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 14:04:53,685 ERROR frappe Could not take error snapshot: invalid syntax (constants.py, line 54)
Site: parentngo
Form Dict: {'doctype': 'Loan Request', 'fields': '["`tabLoan Request`.`status`","`tabLoan Request`.`name`","`tabLoan Request`.`owner`","`tabLoan Request`.`creation`","`tabLoan Request`.`modified`","`tabLoan Request`.`modified_by`","`tabLoan Request`.`_user_tags`","`tabLoan Request`.`_comments`","`tabLoan Request`.`_assign`","`tabLoan Request`.`_liked_by`","`tabLoan Request`.`docstatus`","`tabLoan Request`.`idx`","`tabLoan Request`.`ngo_name`","`tabLoan Request`.`total_loan_amount`","`tabLoan Request`.`loan_type`","`tabLoan Request`.`requested_loan_amount`","`tabLoan Request`.`loan_program`"]', 'filters': '[]', 'order_by': '`tabLoan Request`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 99, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/__init__.py", line 4, in <module>
    from .constants import *
  File "/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/constants.py", line 54
    <<<<<<< HEAD
    ^^
SyntaxError: invalid syntax

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/__init__.py", line 4, in <module>
    from .constants import *
  File "/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/constants.py", line 54
    <<<<<<< HEAD
    ^^
SyntaxError: invalid syntax
2025-06-15 14:04:53,692 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'doctype': 'Loan Request', 'fields': '["`tabLoan Request`.`status`","`tabLoan Request`.`name`","`tabLoan Request`.`owner`","`tabLoan Request`.`creation`","`tabLoan Request`.`modified`","`tabLoan Request`.`modified_by`","`tabLoan Request`.`_user_tags`","`tabLoan Request`.`_comments`","`tabLoan Request`.`_assign`","`tabLoan Request`.`_liked_by`","`tabLoan Request`.`docstatus`","`tabLoan Request`.`idx`","`tabLoan Request`.`ngo_name`","`tabLoan Request`.`total_loan_amount`","`tabLoan Request`.`loan_type`","`tabLoan Request`.`requested_loan_amount`","`tabLoan Request`.`loan_program`"]', 'filters': '[]', 'order_by': '`tabLoan Request`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 161, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/__init__.py", line 4, in <module>
    from .constants import *
  File "/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/constants.py", line 54
    <<<<<<< HEAD
    ^^
SyntaxError: invalid syntax
2025-06-16 11:03:50,896 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-16 14:25:29,190 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-16 14:27:20,613 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer_id': 'undefined', 'cmd': 'nexus.api.v1.customers.endpoints.get_customer'}
2025-06-16 14:27:22,157 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer_id': 'undefined', 'cmd': 'nexus.api.v1.customers.endpoints.get_customer'}
2025-06-16 14:37:31,926 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-06-16', 'delivery_date': '2025-06-26', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-06-16 14:37:54,112 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-06-16', 'delivery_date': '2025-06-26', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
