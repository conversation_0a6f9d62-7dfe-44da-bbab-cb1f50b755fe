2025-05-07 17:37:02,697 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'item_name': 'd', 'item_code': 'd', 'item_group': 'd', 'description': 'ddd'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-11 16:32:09,753 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-11 16:32:59,766 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-11 16:35:32,838 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 11:48:48,239 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 13:46:38,531 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:24:29,850 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:08,849 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:11,130 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:20,494 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'user1', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:22,122 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'user1', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:26,515 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': '<EMAIL>', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:42,014 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'cmd': 'login', 'usr': 'user1', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:51,325 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': '<EMAIL>', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-13 19:05:23,912 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': '<EMAIL>', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-13 19:07:07,497 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:08:11,082 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:08:25,424 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:09:10,562 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:28:29,933 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:29:54,284 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:33:37,156 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'fff'}
2025-05-14 17:34:39,256 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'fff'}
2025-05-14 17:43:27,947 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'uuu'}
2025-05-14 18:03:12,442 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ddd', 'parent_category': 'All Item Groups', 'is_group': 0}
2025-05-14 18:03:34,784 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ddd', 'parent_category': 'All Item Groups', 'is_group': 0}
2025-05-14 18:03:53,524 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 18:03:57,825 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 18:04:42,761 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'id': 'ee', 'parent_category': 'All Item Groups', 'is_group': 0}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 22:17:38,622 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 22:18:00,538 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'xx'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 22:41:00,933 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 22:44:16,789 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'jjjj', 'parent_category': 'All Item Groups', 'is_group': 0}
2025-05-14 22:45:47,506 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 22:45:51,777 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 22:49:04,362 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test'}
2025-05-14 22:49:12,557 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2'}
2025-05-14 22:55:13,580 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'uuu'}
2025-05-16 23:50:01,970 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-16 23:54:00,863 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2'}
2025-05-16 23:55:16,138 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'trgft'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-16 23:55:19,337 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'trgft'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-16 23:55:30,394 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'trgft'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-18 17:46:49,343 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'eeee'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-21 16:35:54,804 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-21 17:05:03,252 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-25 11:10:25,046 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:10:29,816 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/v1/orders/urls.py", line 3, in <module>
    order
NameError: name 'order' is not defined
2025-05-25 11:10:30,130 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:10:31,764 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/v1/orders/urls.py", line 3, in <module>
    order
NameError: name 'order' is not defined
2025-05-25 11:10:31,833 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:10:33,204 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/v1/orders/urls.py", line 3, in <module>
    order
NameError: name 'order' is not defined
2025-05-25 11:12:55,540 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:12:56,731 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 11:12:56,815 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:12:58,135 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 11:13:48,911 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'page': '1', 'per_page': '20', 'sort_by': 'issue_date', 'sort_order': 'desc'}
2025-05-25 11:19:54,707 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'page': '1', 'per_page': '20', 'sort_by': 'issue_date', 'sort_order': 'asc'}
2025-05-25 11:19:55,482 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'page': '1', 'per_page': '20', 'sort_by': 'issue_date', 'sort_order': 'desc'}
2025-05-25 12:39:52,913 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 12:39:53,375 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 12:39:53,502 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 12:39:53,889 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 12:39:53,979 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 12:39:54,496 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 12:41:10,396 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ORD-005'}
2025-05-25 12:41:53,426 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ORD-004'}
2025-05-25 13:02:48,777 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:50,449 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:50,911 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:51,326 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:51,631 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:51,879 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,070 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,441 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,631 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,800 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:53,480 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:54,768 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:54,949 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:55,144 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:55,310 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:55,511 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:56,357 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,241 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,414 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,745 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,927 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:58,199 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:58,431 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 14:40:27,333 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2'}
2025-05-25 14:43:10,632 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-25 14:43:12,915 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-25 14:54:46,717 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-25 14:56:27,748 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 114, in application
    response = frappe.api.handle(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/api/__init__.py", line 55, in handle
    return build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7e12b58931c0> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 132, in application
    response = handle_exception(e)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 74, in handle_api_exception
    response = handle_frappe_exception(error)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 334, in handle_exception
    response = frappe.utils.response.report_error(http_status_code)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 58, in report_error
    response = build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7e12b58931c0> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 50, in handle_response
    prepare_response(response)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 58, in prepare_response
    response.status_code = status_code
AttributeError: 'NoneType' object has no attribute 'status_code'
2025-05-26 11:10:16,840 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 11:10:17,178 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 11:10:17,190 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 11:10:17,232 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 11:11:15,979 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********', 'jwt_only': True, 'cmd': 'iam.api.v1.auth.login'}
2025-05-26 11:16:47,803 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********', 'jwt_only': True, 'cmd': 'iam.api.v1.auth.login'}
2025-05-26 11:25:56,178 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-26 11:58:05,575 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-26 11:58:54,831 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 114, in application
    response = frappe.api.handle(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/api/__init__.py", line 55, in handle
    return build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7d5e7cb93070> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 132, in application
    response = handle_exception(e)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 74, in handle_api_exception
    response = handle_frappe_exception(error)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 334, in handle_exception
    response = frappe.utils.response.report_error(http_status_code)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 58, in report_error
    response = build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7d5e7cb93070> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 50, in handle_response
    prepare_response(response)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 58, in prepare_response
    response.status_code = status_code
AttributeError: 'NoneType' object has no attribute 'status_code'
2025-05-26 12:13:35,588 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:14:18,855 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:18:42,934 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:19:54,794 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:27:21,326 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'msm', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:45:27,119 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 13:29:20,530 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c'}
2025-05-26 13:38:57,413 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-26 13:39:29,140 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 13:50:14,473 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 13:58:32,533 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': '', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:16:09,103 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'order_id': 'SAL-ORD-2025-00005', 'cmd': 'nexus.api.v1.orders.endpoints.get_order'}
2025-05-26 14:21:35,020 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:23:33,302 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:24:05,403 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:29:08,497 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:42:01,457 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'p'}
2025-05-26 14:42:20,293 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 14:42:20,333 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 14:55:24,528 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pl', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 15:42:43,485 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 15:42:43,533 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 15:48:20,407 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 15:48:20,488 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 17:17:02,362 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'CUST-001', 'customer_name': 'John Doe', 'transaction_date': '2025-05-26', 'delivery_date': '2025-05-29', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-05-26 17:29:04,800 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 17:29:04,851 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 22:34:19,187 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 22:34:19,331 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 23:33:05,188 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'Grant Plastics Ltd.', 'customer_name': 'Grant Plastics Ltd.', 'transaction_date': '2025-05-26', 'delivery_date': '2025-05-29', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-05-27 15:20:05,055 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'Palmer Productions Ltd.', 'customer_name': 'Palmer Productions Ltd.', 'transaction_date': '2025-05-27', 'delivery_date': '2025-05-31', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-06-15 11:23:45,366 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:47,265 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:48,206 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:49,233 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:49,883 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:50,808 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:52,214 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:53,266 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:55,088 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:23:55,743 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:04,082 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:06,558 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:09,272 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:10,843 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:10,885 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:14,389 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:18,450 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:20,356 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:22,779 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:23,820 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:25,313 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:24:44,264 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:25:01,131 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-15 11:25:02,306 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-16 14:25:29,190 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-06-16 14:27:20,613 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer_id': 'undefined', 'cmd': 'nexus.api.v1.customers.endpoints.get_customer'}
2025-06-16 14:27:22,157 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer_id': 'undefined', 'cmd': 'nexus.api.v1.customers.endpoints.get_customer'}
2025-06-16 14:37:31,926 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-06-16', 'delivery_date': '2025-06-26', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-06-16 14:37:54,112 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-06-16', 'delivery_date': '2025-06-26', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
