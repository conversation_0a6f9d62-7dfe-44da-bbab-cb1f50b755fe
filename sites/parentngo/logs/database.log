2025-05-11 13:04:28,669 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-11 13:04:30,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-11 13:04:32,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-13 15:33:46,175 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-13 15:33:48,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-13 15:33:50,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-13 15:33:52,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Work` MODIFY `loan_amount` decimal(21,9) not null default 0
2025-05-13 15:33:53,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` ADD COLUMN `cash_flow_rating` decimal(3,2)
2025-05-13 15:33:53,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` MODIFY `system_rating` decimal(3,2), MODIFY `financial_records_rating` decimal(3,2), MODIFY `final_accounts_rating` decimal(3,2), MODIFY `financial_cycle_rating` decimal(3,2), MODIFY `previous_works_rating` decimal(3,2)
2025-05-13 15:33:54,927 WARNING database DDL Query made to DB:
create table `tabAttachments Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-13 16:17:08,868 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-13 16:17:11,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-13 16:17:13,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-13 16:47:31,779 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-13 16:47:34,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-13 16:47:36,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-13 16:54:47,924 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-13 16:54:49,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-13 16:54:52,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-13 16:54:55,261 WARNING database DDL Query made to DB:
create table `tabAttachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140),
`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-15 15:36:33,897 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-15 15:36:35,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-15 15:36:37,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-15 15:36:38,913 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Work` MODIFY `loan_amount` decimal(21,9) not null default 0
2025-05-15 15:36:39,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation Details` ADD COLUMN `financial_investigation_rating` int(11) not null default 0
2025-05-15 15:36:39,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` MODIFY `microfinance_rating` decimal(3,2), MODIFY `organizational_rating` decimal(3,2), MODIFY `administrative_rating` decimal(3,2), MODIFY `activities_rating` decimal(3,2), MODIFY `address_rating` decimal(3,2)
2025-05-15 15:38:29,152 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-15 15:38:30,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-15 15:38:32,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-15 15:38:34,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` ADD COLUMN `fa_report_rating` varchar(140), ADD COLUMN `final_account_rating` int(11) not null default 0
2025-05-15 15:38:34,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` MODIFY `financial_records_rating` int(11) not null default 0
2025-05-15 19:35:31,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` ADD COLUMN `ceo_full_name` varchar(140), ADD COLUMN `ceo_nid` varchar(140)
2025-05-15 19:35:31,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-15 19:46:56,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` ADD COLUMN `ceo_nid_front` text, ADD COLUMN `ceo_nid_back` text
2025-05-15 19:46:56,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-15 19:51:10,918 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-15 19:51:12,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-15 19:51:15,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-15 19:51:17,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-16 20:17:38,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` ADD COLUMN `treasurer_iscore_status` varchar(140), ADD COLUMN `treasurer_iscore_result` int(11) not null default 0, ADD COLUMN `treasurer_iscore_report` text, ADD COLUMN `ceo_iscore_status` varchar(140), ADD COLUMN `ceo_iscore_result` int(11) not null default 0, ADD COLUMN `ceo_iscore_report` text
2025-05-16 20:17:38,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-16 20:20:51,845 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-16 20:20:53,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-16 20:20:55,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-16 20:20:57,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-16 22:41:32,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` ADD COLUMN `treasurer_iscore_doc` varchar(140), ADD COLUMN `ceo_iscore_doc` varchar(140)
2025-05-16 22:41:32,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-16 22:41:42,313 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-16 22:41:43,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-16 22:41:45,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-16 22:41:48,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-19 14:44:35,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` ADD COLUMN `treasurer_iscore_report_id` varchar(140), ADD COLUMN `attachment_id` varchar(140)
2025-05-19 14:44:35,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-19 14:55:55,884 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-19 14:55:57,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-19 14:55:59,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-19 14:56:01,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-19 16:08:23,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` ADD COLUMN `treasurer_iscore_loans_number` int(11) not null default 0, ADD COLUMN `treasurer_iscore_total_balance` decimal(21,9) not null default 0, ADD COLUMN `ceo_iscore_loans_number` int(11) not null default 0, ADD COLUMN `ceo_iscore_total_balance` decimal(21,9) not null default 0
2025-05-19 16:08:23,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-19 16:08:58,262 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-19 16:08:59,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-19 16:09:01,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-19 16:09:03,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0
2025-05-20 13:50:12,735 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0
2025-05-20 13:55:31,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0
2025-05-20 16:11:12,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` ADD COLUMN `ceo_iscore_report_id` varchar(140)
2025-05-20 16:11:12,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0
2025-05-20 16:12:46,791 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-20 16:12:48,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-20 16:12:50,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-20 16:12:53,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0
2025-05-20 16:27:07,476 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-20 16:27:09,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-20 16:27:11,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-20 16:32:30,192 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-20 16:32:31,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-20 16:32:33,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-22 12:04:49,507 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-22 12:04:51,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-22 12:04:53,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-22 12:04:55,488 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Work` MODIFY `loan_amount` decimal(21,9) not null default 0
2025-05-22 12:04:56,650 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` MODIFY `administrative_rating` decimal(3,2), MODIFY `microfinance_rating` decimal(3,2), MODIFY `activities_rating` decimal(3,2), MODIFY `organizational_rating` decimal(3,2), MODIFY `address_rating` decimal(3,2)
2025-05-22 12:04:57,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` MODIFY `financial_records_rating` decimal(3,2), MODIFY `system_rating` decimal(3,2), MODIFY `previous_works_rating` decimal(3,2), MODIFY `final_accounts_rating` decimal(3,2), MODIFY `financial_cycle_rating` decimal(3,2)
2025-05-22 12:04:57,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-05-22 12:13:03,418 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-22 12:13:05,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-22 12:13:06,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-22 14:05:11,459 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-22 14:05:13,178 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-22 14:05:15,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-22 14:05:16,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Work` MODIFY `loan_amount` decimal(21,9) not null default 0
2025-05-22 14:05:18,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` MODIFY `microfinance_rating` decimal(3,2), MODIFY `administrative_rating` decimal(3,2), MODIFY `address_rating` decimal(3,2), MODIFY `organizational_rating` decimal(3,2), MODIFY `activities_rating` decimal(3,2)
2025-05-22 14:06:07,541 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-22 14:06:09,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-22 14:06:11,363 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-22 14:06:13,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` MODIFY `financial_records_rating` int(11) not null default 0
2025-05-22 14:29:20,721 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-22 14:29:22,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-22 14:29:24,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-22 14:41:06,325 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-22 14:41:07,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-22 14:41:09,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-22 16:05:15,401 WARNING database DDL Query made to DB:
truncate `tabHas Role`
2025-05-22 16:05:39,999 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-22 16:05:41,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-22 16:05:43,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-27 15:27:04,670 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-27 15:27:05,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-27 15:27:06,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-27 15:27:08,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0
2025-06-01 11:20:30,960 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-01 11:20:32,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-01 11:20:33,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-01 11:20:35,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` ADD COLUMN `bod_meetings_record_rating` varchar(140), ADD COLUMN `ga_meetings_record_rating` varchar(140), ADD COLUMN `bwl_record_rating` varchar(140), ADD COLUMN `administrative_records_rating` int(11) not null default 0
2025-06-01 11:20:36,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-01 11:25:57,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabFinancial Investigation` ADD COLUMN `cash_flow_form_status` varchar(140)
2025-06-01 14:50:39,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-01 14:50:39,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-01 14:51:44,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-01 14:51:44,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-01 14:52:31,589 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-01 14:52:31,589 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-01 16:04:42,618 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-01 16:04:45,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-01 16:04:49,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-01 16:04:56,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` MODIFY `remaining_principal_amount` decimal(21,9) not null default 0, MODIFY `monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `remaining_interest_amount` decimal(21,9) not null default 0, MODIFY `monthly_principal_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `undisbursed_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `last_monthly_principal_amount` decimal(21,9) not null default 0, MODIFY `monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0
2025-06-01 16:04:58,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-10 14:34:28,311 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-10 14:34:29,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-10 14:34:30,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-10 14:34:31,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` MODIFY `loan_amount` decimal(21,9) not null default 0, MODIFY `monthly_principal_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `undisbursed_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `remaining_interest_amount` decimal(21,9) not null default 0, MODIFY `monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `remaining_principal_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_principal_amount` decimal(21,9) not null default 0
2025-06-10 14:34:32,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0
2025-06-10 14:58:23,088 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-10 14:58:24,197 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-10 14:58:25,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-10 14:58:26,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` ADD COLUMN `ownership_type_owned_rating` varchar(140), ADD COLUMN `ownership_type_rental_rating` varchar(140), ADD COLUMN `ownership_type_allocated_rating` varchar(140), ADD COLUMN `ngo_activities_rating` varchar(140), ADD COLUMN `microfinance_activities_status` varchar(140), ADD COLUMN `microfinance_activities_excellent_rating` varchar(140), ADD COLUMN `microfinance_activities_good_rating` varchar(140), ADD COLUMN `manager_full_name` varchar(140), ADD COLUMN `manager_employment_date` date, ADD COLUMN `manager_experience_years` int(11) not null default 0, ADD COLUMN `manager_insurance_status` varchar(140), ADD COLUMN `manager_rating` varchar(140) default '0', ADD COLUMN `accountant_full_name` varchar(140), ADD COLUMN `accountant_employment_date` date, ADD COLUMN `accountant_experience_years` int(11) not null default 0, ADD COLUMN `accountant_insurance_status` varchar(140), ADD COLUMN `accountant_rating` varchar(140) default '0', ADD COLUMN `officer_full_name` varchar(140), ADD COLUMN `officer_employment_date` date, ADD COLUMN `officer_experience_years` int(11) not null default 0, ADD COLUMN `officer_insurance_status` varchar(140), ADD COLUMN `officer_rating` varchar(140) default '0', ADD COLUMN `cashier_full_name` varchar(140), ADD COLUMN `cashier_employment_date` date, ADD COLUMN `cashier_experience_years` int(11) not null default 0, ADD COLUMN `cashier_insurance_status` varchar(140), ADD COLUMN `cashier_rating` varchar(140) default '0', ADD COLUMN `investigator_full_name` varchar(140), ADD COLUMN `investigator_employment_date` date, ADD COLUMN `investigator_experience_years` int(11) not null default 0, ADD COLUMN `investigator_insurance_status` varchar(140), ADD COLUMN `investigator_rating` varchar(140) default '0', ADD COLUMN `administrative_apparatus_rating` int(11) not null default 0, ADD COLUMN `general_assembly_memebers_rating` varchar(140), ADD COLUMN `general_assembly_meetings_rating` varchar(140), ADD COLUMN `general_assembly_females_members_rating` varchar(140), ADD COLUMN `directors_board_meetings_rating` varchar(140), ADD COLUMN `directors_board_females_members_rating` varchar(140), ADD COLUMN `directors_board_members_rating_5_9` varchar(140), ADD COLUMN `directors_board_members_rating_11_15` varchar(140), ADD COLUMN `organizational_chart_rating` int(11) not null default 0
2025-06-10 14:58:26,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` MODIFY `address_rating` decimal(3,2)
2025-06-10 14:58:27,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-10 15:01:25,735 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-10 15:01:26,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-10 15:01:27,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-10 15:01:29,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0
2025-06-10 18:21:09,378 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parent varchar(140)
2025-06-10 18:21:09,380 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parenttype varchar(140)
2025-06-10 18:21:09,381 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parentfield varchar(140)
2025-06-10 18:21:09,501 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` ADD COLUMN `bm_iscore_status` varchar(140), ADD COLUMN `bm_iscore_result` int(11) not null default 0, ADD COLUMN `bm_iscore_loans_number` int(11) not null default 0, ADD COLUMN `bm_iscore_total_balance` decimal(21,9) not null default 0, ADD COLUMN `bm_iscore_report` text, ADD COLUMN `bm_iscore_doc` varchar(140), ADD COLUMN `bm_iscore_report_id` varchar(140)
2025-06-10 18:21:34,903 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parent varchar(140)
2025-06-10 18:21:34,904 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parenttype varchar(140)
2025-06-10 18:21:34,904 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parentfield varchar(140)
2025-06-10 18:21:35,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-06-10 18:56:45,939 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-10 18:56:46,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-10 18:56:48,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-10 18:56:49,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-06-10 19:01:37,848 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parent varchar(140)
2025-06-10 19:01:37,849 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parenttype varchar(140)
2025-06-10 19:01:37,850 WARNING database DDL Query made to DB:
alter table `tabLR Board Member Details` add column if not exists parentfield varchar(140)
2025-06-10 19:01:37,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-06-10 23:24:19,606 WARNING database DDL Query made to DB:
alter table `tabBoard Member Details` add column if not exists parent varchar(140)
2025-06-10 23:24:19,609 WARNING database DDL Query made to DB:
alter table `tabBoard Member Details` add column if not exists parenttype varchar(140)
2025-06-10 23:24:19,609 WARNING database DDL Query made to DB:
alter table `tabBoard Member Details` add column if not exists parentfield varchar(140)
2025-06-10 23:24:19,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabBoard Member Details` ADD COLUMN `bm_iscore_report` text, ADD COLUMN `bm_iscore_result` int(11) not null default 0
2025-06-15 14:08:59,938 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-15 14:09:00,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-15 14:09:01,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-15 14:09:03,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` MODIFY `monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `remaining_principal_amount` decimal(21,9) not null default 0, MODIFY `monthly_principal_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_principal_amount` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `remaining_interest_amount` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `undisbursed_amount` decimal(21,9) not null default 0
2025-06-15 14:09:03,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-06-15 14:09:04,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0
2025-06-15 16:23:15,192 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-15 16:23:16,237 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-15 16:23:17,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-15 16:23:18,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation Details` ADD COLUMN `administrative_investigation_rating` int(11) not null default 0
2025-06-15 16:23:18,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` ADD COLUMN `administrative_investigation_rating` int(11) not null default 0
2025-06-15 16:23:19,276 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-06-16 10:56:12,043 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-16 10:56:13,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-16 10:56:14,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-16 10:56:15,089 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-06-16 15:53:10,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` MODIFY `general_assembly_meetings_rating` varchar(140)
